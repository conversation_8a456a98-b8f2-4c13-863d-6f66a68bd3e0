import { createClient } from '@/lib/supabase/server'
import { getServerCart } from '@/lib/cart-server'
import { CartClient } from '@/components/cart/cart-client'

interface CartPageProps {
  params: Promise<{ locale: string }>;
}

export default async function CartPage({ params }: CartPageProps) {
  const { locale } = await params
  
  const supabase = await createClient()
  
  try {
    // Get current user - handle guest users gracefully
    let user = null
    try {
      const { data: userData, error: userError } = await supabase.auth.getUser()
      if (userError &&
          !userError.message.includes('Auth session missing') &&
          !userError.message.includes('Invalid Refresh Token')) {
        console.error('Unexpected auth error in CartPage:', userError)
      }
      user = userData?.user || null
    } catch {
      // Guest users - continue with null user
      user = null
    }

    // Get cart
    const cart = await getServerCart(user?.id)

    return <CartClient cart={cart} locale={locale} />
  } catch (error) {
    // Only log unexpected errors
    if (error instanceof Error &&
        !error.message.includes('Auth session missing') &&
        !error.message.includes('Invalid Refresh Token')) {
      console.error('Error in CartPage:', error)
    }
    // Return empty cart state if there's an error
    return <CartClient cart={null} locale={locale} />
  }
}
