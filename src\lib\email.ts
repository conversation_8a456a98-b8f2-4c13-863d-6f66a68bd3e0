import nodemailer from 'nodemailer';
import { formatCurrency, formatDateTime } from './utils';

// Temporary types until we set up proper type imports
interface UserAddress {
  first_name: string;
  last_name: string;
  street_address: string;
  postal_code: string;
  city: string;
  country: string;
}

interface Product {
  title: string;
}

interface OrderItem {
  quantity: number;
  unit_price: number;
  total_price: number;
  product: Product;
}

interface Order {
  id: string;
  order_number?: string;
  email: string;
  status: string;
  subtotal: number;
  shipping_cost: number;
  discount_amount: number;
  total_amount: number;
  created_at: string;
  shipping_address: UserAddress;
  billing_address: UserAddress;
}

// Create transporter
const createTransporter = () => {
  console.log('📧 Email: Creating SMTP transporter...');

  // Check environment variables
  const requiredVars = {
    SMTP_HOST: process.env.SMTP_HOST,
    SMTP_USER: process.env.SMTP_USER,
    SMTP_PASS: process.env.SMTP_PASS,
    SMTP_FROM: process.env.SMTP_FROM,
    SMTP_FROM_NAME: process.env.SMTP_FROM_NAME
  };

  console.log('📧 Email: Environment variables check:', {
    SMTP_HOST: requiredVars.SMTP_HOST ? '✓ Set' : '✗ Missing',
    SMTP_USER: requiredVars.SMTP_USER ? '✓ Set' : '✗ Missing',
    SMTP_PASS: requiredVars.SMTP_PASS ? '✓ Set' : '✗ Missing',
    SMTP_FROM: requiredVars.SMTP_FROM ? '✓ Set' : '✗ Missing',
    SMTP_FROM_NAME: requiredVars.SMTP_FROM_NAME ? '✓ Set' : '✗ Missing',
    SMTP_PORT: process.env.SMTP_PORT || '587',
    SMTP_SECURE: process.env.SMTP_SECURE || 'false'
  });

  if (!process.env.SMTP_HOST || !process.env.SMTP_USER || !process.env.SMTP_PASS) {
    const error = 'SMTP configuration is incomplete';
    console.error('📧 Email: Configuration error:', error);
    throw new Error(error);
  }

  const config = {
    host: process.env.SMTP_HOST,
    port: parseInt(process.env.SMTP_PORT || '587'),
    secure: process.env.SMTP_SECURE === 'true',
    auth: {
      user: process.env.SMTP_USER,
      pass: process.env.SMTP_PASS,
    },
    debug: process.env.NODE_ENV === 'development',
    logger: process.env.NODE_ENV === 'development'
  };

  console.log('📧 Email: SMTP configuration:', {
    host: config.host,
    port: config.port,
    secure: config.secure,
    user: config.auth.user,
    debug: config.debug
  });

  return nodemailer.createTransport(config);
};

// Test email function for debugging
export const testEmailConfiguration = async () => {
  console.log('📧 Email: Testing email configuration...');

  try {
    const transporter = createTransporter();
    console.log('📧 Email: Transporter created for test');

    // Test connection
    console.log('📧 Email: Testing SMTP connection...');
    const isConnected = await transporter.verify();
    console.log('📧 Email: SMTP connection test result:', isConnected);

    // Send test email
    const testMailOptions = {
      from: `${process.env.SMTP_FROM_NAME} <${process.env.SMTP_FROM}>`,
      to: process.env.NEXT_PUBLIC_COMPANY_EMAIL || '<EMAIL>',
      subject: 'Test Email - PrimeCaffe Configuration',
      html: `
        <h2>Test Email</h2>
        <p>This is a test email to verify the email configuration.</p>
        <p>Sent at: ${new Date().toISOString()}</p>
        <p>Environment: ${process.env.NODE_ENV}</p>
      `
    };

    console.log('📧 Email: Sending test email with options:', {
      from: testMailOptions.from,
      to: testMailOptions.to,
      subject: testMailOptions.subject
    });

    const result = await transporter.sendMail(testMailOptions);
    console.log('📧 Email: Test email sent successfully:', {
      messageId: result.messageId,
      response: result.response,
      accepted: result.accepted,
      rejected: result.rejected
    });

    return { success: true, result };

  } catch (error) {
    console.error('📧 Email: Test email failed:', error);
    console.error('📧 Email: Test error details:', {
      name: error instanceof Error ? error.name : 'Unknown',
      message: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      code: (error as { code?: string })?.code,
      command: (error as { command?: string })?.command,
      response: (error as { response?: string })?.response,
      responseCode: (error as { responseCode?: number })?.responseCode
    });
    return { success: false, error };
  }
};

// Email templates
export const sendOrderConfirmationEmail = async (
  order: Order & {
    items?: (OrderItem & { product?: { title: string }, products?: { title: string } })[]
    order_items?: (OrderItem & { product?: { title: string }, products?: { title: string } })[]
  }
) => {
  console.log('📧 Email: Starting order confirmation email send...');
  console.log('📧 Email: Order details:', {
    orderId: order.id,
    email: order.email,
    itemsCount:
      (order.items || (order as unknown as { order_items?: OrderItem[] }).order_items)?.length || 0,
    totalAmount: order.total_amount
  });

  try {
    const transporter = createTransporter();
    console.log('📧 Email: Transporter created successfully');

    // Test connection
    console.log('📧 Email: Testing SMTP connection...');
    await transporter.verify();
    console.log('📧 Email: SMTP connection verified successfully');

  const itemsList =
    order.items || (order as unknown as { order_items?: OrderItem[] }).order_items || []
  const itemsHtml = itemsList
    .map(
      (item) => `
        <tr>
          <td style="padding: 8px; border-bottom: 1px solid #eee;">${item.product?.title || (item as unknown as { products?: { title?: string } }).products?.title || 'Product'}</td>
          <td style="padding: 8px; border-bottom: 1px solid #eee; text-align: center;">${item.quantity}</td>
          <td style="padding: 8px; border-bottom: 1px solid #eee; text-align: right;">${formatCurrency(item.unit_price)}</td>
          <td style="padding: 8px; border-bottom: 1px solid #eee; text-align: right;">${formatCurrency(item.total_price)}</td>
        </tr>
      `
    )
    .join('');

  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Bestellbestätigung - PrimeCaffe</title>
    </head>
    <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
      <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="text-align: center; margin-bottom: 30px;">
          <h1 style="color: #d97706;">PrimeCaffe</h1>
        </div>
        
        <h2>Vielen Dank für Ihre Bestellung!</h2>
        
        <p>Liebe/r Kunde/in,</p>
        
        <p>wir haben Ihre Bestellung erhalten und bearbeiten sie bereits. Hier sind die Details:</p>
        
        <div style="background: #f9f9f9; padding: 20px; margin: 20px 0; border-radius: 5px;">
          <h3>Bestellnummer: ${order.order_number || order.id}</h3>
          <p><strong>Bestelldatum:</strong> ${formatDateTime(order.created_at)}</p>
          <p><strong>Status:</strong> ${order.status}</p>
        </div>
        
        <h3>Bestellte Artikel:</h3>
        <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
          <thead>
            <tr style="background: #f5f5f5;">
              <th style="padding: 12px; text-align: left; border-bottom: 2px solid #ddd;">Artikel</th>
              <th style="padding: 12px; text-align: center; border-bottom: 2px solid #ddd;">Anzahl</th>
              <th style="padding: 12px; text-align: right; border-bottom: 2px solid #ddd;">Preis</th>
              <th style="padding: 12px; text-align: right; border-bottom: 2px solid #ddd;">Gesamt</th>
            </tr>
          </thead>
          <tbody>
            ${itemsHtml}
          </tbody>
        </table>
        
        <div style="text-align: right; margin: 20px 0;">
          <p><strong>Zwischensumme: ${formatCurrency(order.subtotal)}</strong></p>
          <p><strong>Versandkosten: ${formatCurrency(order.shipping_cost)}</strong></p>
          ${order.discount_amount > 0 ? `<p><strong>Rabatt: -${formatCurrency(order.discount_amount)}</strong></p>` : ''}
          <p style="font-size: 18px; color: #d97706;"><strong>Gesamtbetrag: ${formatCurrency(order.total_amount)}</strong></p>
        </div>
        
        <div style="background: #f0f9ff; padding: 20px; margin: 20px 0; border-radius: 5px;">
          <h3>Lieferadresse:</h3>
          <p>
            ${(order.shipping_address as UserAddress).first_name} ${(order.shipping_address as UserAddress).last_name}<br>
            ${(order.shipping_address as UserAddress).street_address}<br>
            ${(order.shipping_address as UserAddress).postal_code} ${(order.shipping_address as UserAddress).city}<br>
            ${(order.shipping_address as UserAddress).country}
          </p>
        </div>
        
        <p>Wir werden Sie per E-Mail informieren, sobald Ihre Bestellung versandt wurde.</p>
        
        <p>Bei Fragen stehen wir Ihnen gerne zur Verfügung.</p>
        
        <p>Mit freundlichen Grüssen,<br>
        Ihr PrimeCaffe Team</p>
        
        <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
        
        <div style="text-align: center; color: #666; font-size: 12px;">
          <p>PrimeCaffe AG<br>
          ${process.env.NEXT_PUBLIC_COMPANY_ADDRESS}<br>
          E-Mail: ${process.env.NEXT_PUBLIC_COMPANY_EMAIL}<br>
          Telefon: ${process.env.NEXT_PUBLIC_COMPANY_PHONE}</p>
        </div>
      </div>
    </body>
    </html>
  `;

    const mailOptions = {
      from: `${process.env.SMTP_FROM_NAME} <${process.env.SMTP_FROM}>`,
      to: order.email,
      subject: `Bestellbestätigung #${order.order_number || order.id} - PrimeCaffe`,
      html,
    };

    console.log('📧 Email: Sending order confirmation email with options:', {
      from: mailOptions.from,
      to: mailOptions.to,
      subject: mailOptions.subject,
      htmlLength: html.length
    });

    const result = await transporter.sendMail(mailOptions);
    console.log('📧 Email: Order confirmation email sent successfully:', {
      messageId: result.messageId,
      response: result.response,
      accepted: result.accepted,
      rejected: result.rejected
    });

    return result;

  } catch (error) {
    console.error('📧 Email: Error sending order confirmation email:', error);
    console.error('📧 Email: Error details:', {
      name: error instanceof Error ? error.name : 'Unknown',
      message: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      code: (error as { code?: string })?.code,
      command: (error as { command?: string })?.command,
      response: (error as { response?: string })?.response,
      responseCode: (error as { responseCode?: number })?.responseCode
    });
    throw error;
  }
};

// Helper function to get shipping email content based on locale
const getShippingEmailContent = (locale: string, order: Order) => {
  switch (locale) {
    case 'it':
      return {
        title: 'Il tuo ordine è stato spedito - PrimeCaffe',
        subject: `Il tuo ordine #${order.order_number || order.id} è stato spedito - PrimeCaffe`,
        heading: 'Il tuo ordine è in viaggio!',
        greeting: 'Gentile Cliente,',
        goodNews: 'buone notizie! Il tuo ordine è stato spedito ed è già in viaggio verso di te.',
        shippingInfoTitle: 'Informazioni di spedizione:',
        orderNumber: 'Numero ordine:',
        trackingNumber: 'Numero di tracciamento:',
        shippingDate: 'Data di spedizione:',
        deliveryAddressTitle: 'Indirizzo di consegna:',
        trackingInfo: 'Puoi tracciare la tua spedizione con il numero di tracciamento sopra indicato.',
        enjoyOrder: 'Speriamo che tu possa goderti il tuo ordine!',
        closing: 'Cordiali saluti,<br>Il Team PrimeCaffe'
      };

    case 'fr':
      return {
        title: 'Votre commande a été expédiée - PrimeCaffe',
        subject: `Votre commande #${order.order_number || order.id} a été expédiée - PrimeCaffe`,
        heading: 'Votre commande est en route !',
        greeting: 'Cher/Chère Client(e),',
        goodNews: 'bonne nouvelle ! Votre commande a été expédiée et est déjà en route vers vous.',
        shippingInfoTitle: 'Informations d\'expédition :',
        orderNumber: 'Numéro de commande :',
        trackingNumber: 'Numéro de suivi :',
        shippingDate: 'Date d\'expédition :',
        deliveryAddressTitle: 'Adresse de livraison :',
        trackingInfo: 'Vous pouvez suivre votre envoi avec le numéro de suivi indiqué ci-dessus.',
        enjoyOrder: 'Nous espérons que vous apprécierez votre commande !',
        closing: 'Cordialement,<br>L\'équipe PrimeCaffe'
      };

    default: // 'de'
      return {
        title: 'Ihre Bestellung wurde versandt - PrimeCaffe',
        subject: `Ihre Bestellung #${order.order_number || order.id} wurde versandt - PrimeCaffe`,
        heading: 'Ihre Bestellung ist unterwegs!',
        greeting: 'Liebe/r Kunde/in,',
        goodNews: 'gute Nachrichten! Ihre Bestellung wurde versandt und ist bereits auf dem Weg zu Ihnen.',
        shippingInfoTitle: 'Versandinformationen:',
        orderNumber: 'Bestellnummer:',
        trackingNumber: 'Sendungsnummer:',
        shippingDate: 'Versanddatum:',
        deliveryAddressTitle: 'Lieferadresse:',
        trackingInfo: 'Sie können Ihre Sendung mit der oben angegebenen Sendungsnummer verfolgen.',
        enjoyOrder: 'Wir hoffen, dass Sie Ihre Bestellung geniessen werden!',
        closing: 'Mit freundlichen Grüssen,<br>Ihr PrimeCaffe Team'
      };
  }
};

export const sendShippingNotificationEmail = async (
  order: Order,
  trackingNumber: string,
  locale: string = 'de'
) => {
  console.log('📧 Email: Starting shipping notification email send...');
  console.log('📧 Email: Shipping notification details:', {
    orderId: order.id,
    email: order.email,
    trackingNumber: trackingNumber,
    totalAmount: order.total_amount,
    locale: locale
  });

  try {
    const transporter = createTransporter();
    console.log('📧 Email: Shipping notification transporter created successfully');

    const content = getShippingEmailContent(locale, order);
    const shippingAddress = order.shipping_address as UserAddress;

    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>${content.title}</title>
      </head>
      <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
        <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #d97706;">PrimeCaffe</h1>
          </div>

          <h2>${content.heading}</h2>

          <p>${content.greeting}</p>

          <p>${content.goodNews}</p>

          <div style="background: #f0f9ff; padding: 20px; margin: 20px 0; border-radius: 5px;">
            <h3>${content.shippingInfoTitle}</h3>
            <p><strong>${content.orderNumber}</strong> ${order.order_number || order.id}</p>
            <p><strong>${content.trackingNumber}</strong> ${trackingNumber}</p>
            <p><strong>${content.shippingDate}</strong> ${formatDateTime(new Date().toISOString())}</p>
          </div>

          <div style="background: #f9f9f9; padding: 20px; margin: 20px 0; border-radius: 5px;">
            <h3>${content.deliveryAddressTitle}</h3>
            <p>
              ${shippingAddress.first_name} ${shippingAddress.last_name}<br>
              ${shippingAddress.street_address}<br>
              ${shippingAddress.postal_code} ${shippingAddress.city}<br>
              ${shippingAddress.country}
            </p>
          </div>

          <p>${content.trackingInfo}</p>

          <p>${content.enjoyOrder}</p>

          <p>${content.closing}</p>

          <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">

          <div style="text-align: center; color: #666; font-size: 12px;">
            <p>PrimeCaffe AG<br>
            ${process.env.NEXT_PUBLIC_COMPANY_ADDRESS}<br>
            E-Mail: ${process.env.NEXT_PUBLIC_COMPANY_EMAIL}<br>
            Telefon: ${process.env.NEXT_PUBLIC_COMPANY_PHONE}</p>
          </div>
        </div>
      </body>
      </html>
    `;

    const shippingMailOptions = {
      from: `${process.env.SMTP_FROM_NAME} <${process.env.SMTP_FROM}>`,
      to: order.email,
      subject: content.subject,
      html,
    };

    console.log('📧 Email: Sending shipping notification email with options:', {
      from: shippingMailOptions.from,
      to: shippingMailOptions.to,
      subject: shippingMailOptions.subject,
      htmlLength: html.length
    });

    const result = await transporter.sendMail(shippingMailOptions);
    console.log('📧 Email: Shipping notification email sent successfully:', {
      messageId: result.messageId,
      response: result.response,
      accepted: result.accepted,
      rejected: result.rejected
    });

    return result;

  } catch (error) {
    console.error('📧 Email: Error sending shipping notification email:', error);
    console.error('📧 Email: Shipping notification error details:', {
      name: error instanceof Error ? error.name : 'Unknown',
      message: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      code: (error as { code?: string })?.code,
      command: (error as { command?: string })?.command,
      response: (error as { response?: string })?.response,
      responseCode: (error as { responseCode?: number })?.responseCode
    });
    throw error;
  }
};

export const sendAdminOrderNotificationEmail = async (
  order: Order & {
    items?: (OrderItem & { product?: { title: string }, products?: { title: string } })[]
    order_items?: (OrderItem & { product?: { title: string }, products?: { title: string } })[]
  }
) => {
  console.log('📧 Email: Starting admin order notification email send...');
  console.log('📧 Email: Admin notification details:', {
    orderId: order.id,
    customerEmail: order.email,
    adminEmail: process.env.NEXT_PUBLIC_COMPANY_EMAIL,
    itemsCount:
      (order.items || (order as unknown as { order_items?: OrderItem[] }).order_items)?.length || 0,
    totalAmount: order.total_amount
  });

  try {
    const transporter = createTransporter();
    console.log('📧 Email: Admin notification transporter created successfully');

  const itemsList =
    order.items || (order as unknown as { order_items?: OrderItem[] }).order_items || []
  const itemsText = itemsList
    .map((item) => `- ${item.product?.title || (item as unknown as { products?: { title?: string } }).products?.title || 'Product'} (${item.quantity}x ${formatCurrency(item.unit_price)})`)
    .join('\n');

  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Neue Bestellung - PrimeCaffe Admin</title>
    </head>
    <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
      <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
        <h2>Neue Bestellung eingegangen</h2>
        
        <div style="background: #f9f9f9; padding: 20px; margin: 20px 0; border-radius: 5px;">
          <h3>Bestelldetails:</h3>
          <p><strong>Bestellnummer:</strong> ${order.order_number || order.id}</p>
          <p><strong>Kunde:</strong> ${order.email}</p>
          <p><strong>Gesamtbetrag:</strong> ${formatCurrency(order.total_amount)}</p>
          <p><strong>Bestelldatum:</strong> ${formatDateTime(order.created_at)}</p>
        </div>
        
        <h3>Bestellte Artikel:</h3>
        <pre style="background: #f5f5f5; padding: 15px; border-radius: 5px;">${itemsText}</pre>
        
        <p><a href="${process.env.NEXT_PUBLIC_APP_URL}/admin/orders/${order.id}" style="background: #d97706; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Bestellung anzeigen</a></p>
      </div>
    </body>
    </html>
  `;

    const adminMailOptions = {
      from: `${process.env.SMTP_FROM_NAME} <${process.env.SMTP_FROM}>`,
      to: process.env.NEXT_PUBLIC_COMPANY_EMAIL,
      subject: `Neue Bestellung #${order.order_number || order.id} - PrimeCaffe`,
      html,
    };

    console.log('📧 Email: Sending admin notification email with options:', {
      from: adminMailOptions.from,
      to: adminMailOptions.to,
      subject: adminMailOptions.subject,
      htmlLength: html.length
    });

    const result = await transporter.sendMail(adminMailOptions);
    console.log('📧 Email: Admin notification email sent successfully:', {
      messageId: result.messageId,
      response: result.response,
      accepted: result.accepted,
      rejected: result.rejected
    });

    return result;

  } catch (error) {
    console.error('📧 Email: Error sending admin notification email:', error);
    console.error('📧 Email: Admin notification error details:', {
      name: error instanceof Error ? error.name : 'Unknown',
      message: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      code: (error as { code?: string })?.code,
      command: (error as { command?: string })?.command,
      response: (error as { response?: string })?.response,
      responseCode: (error as { responseCode?: number })?.responseCode
    });
    throw error;
  }
};
